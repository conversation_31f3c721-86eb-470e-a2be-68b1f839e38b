"use client";

import React, { useState } from "react";
import { useAuth } from "@/lib/auth/useAuth";
import { Session, DeviceInfo } from "@/types/auth";

interface SessionManagerProps {
  className?: string;
}

export const SessionManager: React.FC<SessionManagerProps> = ({ className = "" }) => {
  const { sessions, refreshSessions, terminateSession, terminateAllSessions } = useAuth();
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const handleTerminateSession = async (sessionId: string) => {
    setIsLoading(true);
    setError(null);
    try {
      await terminateSession(sessionId);
      await refreshSessions();
    } catch (err) {
      setError(err instanceof Error ? err.message : "Failed to terminate session");
    } finally {
      setIsLoading(false);
    }
  };

  const handleTerminateAllSessions = async () => {
    if (!confirm("Are you sure you want to terminate all sessions? This will log you out.")) {
      return;
    }
    
    setIsLoading(true);
    setError(null);
    try {
      await terminateAllSessions();
    } catch (err) {
      setError(err instanceof Error ? err.message : "Failed to terminate all sessions");
    } finally {
      setIsLoading(false);
    }
  };

  const parseDeviceInfo = (deviceInfoStr: string | null): DeviceInfo => {
    if (!deviceInfoStr) return {};
    try {
      return JSON.parse(deviceInfoStr);
    } catch {
      return {};
    }
  };

  const formatDate = (dateStr: string) => {
    return new Date(dateStr).toLocaleString();
  };

  const getDeviceDescription = (deviceInfo: DeviceInfo): string => {
    if (deviceInfo.user_agent) {
      // Simple device detection
      const ua = deviceInfo.user_agent.toLowerCase();
      if (ua.includes('mobile')) return '📱 Mobile Device';
      if (ua.includes('tablet')) return '📱 Tablet';
      if (ua.includes('chrome')) return '💻 Chrome Browser';
      if (ua.includes('firefox')) return '🦊 Firefox Browser';
      if (ua.includes('safari')) return '🧭 Safari Browser';
      if (ua.includes('edge')) return '🌐 Edge Browser';
      return '💻 Desktop Browser';
    }
    return '🖥️ Unknown Device';
  };

  return (
    <div className={`bg-[#071922] p-6 rounded-[20px] ${className}`}>
      <div className="flex justify-between items-center mb-6">
        <h2 className="text-xl font-bold text-white font-mina">Active Sessions</h2>
        <button
          onClick={refreshSessions}
          disabled={isLoading}
          className="px-4 py-2 bg-[#DBD2CD] text-[#071922] rounded-lg font-medium hover:bg-[#C5B8B1] transition-colors disabled:opacity-50"
        >
          🔄 Refresh
        </button>
      </div>

      {error && (
        <div className="mb-4 p-3 bg-red-500/20 border border-red-500/30 rounded-lg text-red-300 text-sm">
          {error}
        </div>
      )}

      <div className="space-y-4">
        {sessions.length === 0 ? (
          <div className="text-center py-8 text-white/70">
            <p>No active sessions found</p>
          </div>
        ) : (
          sessions.map((session: Session) => {
            const deviceInfo = parseDeviceInfo(session.device_info);
            return (
              <div
                key={session.session_id}
                className="bg-[#171717] p-4 rounded-lg border border-white/10"
              >
                <div className="flex justify-between items-start mb-3">
                  <div className="flex-1">
                    <div className="flex items-center gap-2 mb-2">
                      <span className="text-lg">{getDeviceDescription(deviceInfo)}</span>
                      <span className="text-white font-medium">Session</span>
                    </div>
                    <div className="text-sm text-white/70 space-y-1">
                      <p>
                        <span className="font-medium">Created:</span> {formatDate(session.created_at)}
                      </p>
                      {session.last_accessed_at && (
                        <p>
                          <span className="font-medium">Last Active:</span> {formatDate(session.last_accessed_at)}
                        </p>
                      )}
                      <p>
                        <span className="font-medium">Expires:</span> {formatDate(session.expires_at)}
                      </p>
                      {deviceInfo.ip_address && (
                        <p>
                          <span className="font-medium">IP:</span> {deviceInfo.ip_address}
                        </p>
                      )}
                    </div>
                  </div>
                  <button
                    onClick={() => handleTerminateSession(session.session_id)}
                    disabled={isLoading}
                    className="px-3 py-1 bg-red-500/20 text-red-300 rounded border border-red-500/30 hover:bg-red-500/30 transition-colors disabled:opacity-50 text-sm"
                  >
                    Terminate
                  </button>
                </div>
                
                <div className="text-xs text-white/50 font-mono bg-black/20 p-2 rounded">
                  Session ID: {session.session_id}
                </div>
              </div>
            );
          })
        )}
      </div>

      {sessions.length > 1 && (
        <div className="mt-6 pt-4 border-t border-white/10">
          <button
            onClick={handleTerminateAllSessions}
            disabled={isLoading}
            className="w-full px-4 py-2 bg-red-500/20 text-red-300 rounded-lg border border-red-500/30 hover:bg-red-500/30 transition-colors disabled:opacity-50 font-medium"
          >
            🚨 Terminate All Sessions (Logout Everywhere)
          </button>
        </div>
      )}

      <div className="mt-4 text-xs text-white/50 text-center">
        Total active sessions: {sessions.length}
      </div>
    </div>
  );
};
